/**
 * Electron 交易客户端 - 前端与 Electron 主进程的通信封装
 * 替代 Tauri 版本，适配新的 Electron 交易系统
 */

// 导入Electron类型
import type { ElectronAPI, ApiResponse } from "../types/electron";

// 基础响应接口
export interface ApiResponse<T = any> {
    success: boolean;
    message: string;
    data: T | null;
    error?: string;
}

// 交易数据类型
export enum TradingDataType {
    Quote = "Quote",
    Ticker = "Ticker",
    OrderBook = "OrderBook",
    BrokerQueue = "BrokerQueue",
    Funds = "Funds",
    Position = "Position",
    Order = "Order"
}

// 股票代码结构
export interface StockCode {
    market: string; // HK, US, SH, SZ
    code: string; // 股票代码
}

// 连接状态
export enum ConnectionStatus {
    Disconnected = "Disconnected",
    Connecting = "Connecting",
    Connected = "Connected",
    Error = "Error"
}

// 任务配置接口
export interface TaskConfig {
    name: string;
    stockCode: string;
    stockName: string;
    market: string;
    strategyConfig: {
        strategyType: string;
        params: Record<string, any>;
    };
    riskConfig: {
        triggerLogic: "any" | "all";
        conditions: Array<{
            type: string;
            params: Record<string, any>;
        }>;
        liquidationStrategy: {
            type: string;
            params: Record<string, any>;
        };
    };
    autoStart?: boolean;
}

// 实时数据事件
export interface RealtimeDataEvent {
    adapter: string;
    data: any;
    timestamp: number;
}

// 连接状态事件
export interface ConnectionStatusEvent {
    adapter: string;
    status: string;
    timestamp: number;
}

// 错误事件
export interface ErrorEvent {
    adapter: string;
    error: string;
    timestamp: number;
}

/**
 * Electron 交易客户端类
 * 提供与 Electron 主进程交易系统的标准接口
 */
export class ElectronTradingClient {
    private eventListeners: Map<string, (event: any, ...args: any[]) => void> = new Map();

    constructor() {
        this.setupEventListeners();
    }

    /**
     * 获取 Electron API
     */
    private get electronAPI(): ElectronAPI {
        if (!window.electronAPI) {
            throw new Error("Electron API 未初始化");
        }
        return window.electronAPI;
    }

    /**
     * 设置事件监听器
     */
    private setupEventListeners(): void {
        // 实时数据事件
        this.electronAPI.on("event:realtimeData", (event, data: RealtimeDataEvent) => {
            const callback = this.eventListeners.get("realtimeData");
            if (callback) {
                callback(event, data);
            }
        });

        // 连接状态事件
        this.electronAPI.on("event:connectionStatus", (event, data: ConnectionStatusEvent) => {
            const callback = this.eventListeners.get("connectionStatus");
            if (callback) {
                callback(event, data);
            }
        });

        // 错误事件
        this.electronAPI.on("event:error", (event, data: ErrorEvent) => {
            const callback = this.eventListeners.get("error");
            if (callback) {
                callback(event, data);
            }
        });

        // 系统状态事件
        this.electronAPI.on("event:systemStatus", (event, data) => {
            const callback = this.eventListeners.get("systemStatus");
            if (callback) {
                callback(event, data);
            }
        });

        // 交易系统事件
        this.electronAPI.on("trading-system:initialized", (event, data) => {
            const callback = this.eventListeners.get("systemInitialized");
            if (callback) {
                callback(event, data);
            }
        });

        this.electronAPI.on("trading-system:heartbeat", (event, data) => {
            const callback = this.eventListeners.get("systemHeartbeat");
            if (callback) {
                callback(event, data);
            }
        });
    }

    // ===== 交易系统管理 =====

    /**
     * 获取交易系统状态
     */
    async getTradingSystemStatus(): Promise<ApiResponse> {
        try {
            const result = await this.electronAPI.tradingSystem.getStatus();
            return result;
        } catch (error) {
            return {
                success: false,
                message: `获取交易系统状态失败: ${error}`,
                data: null
            };
        }
    }

    /**
     * 初始化交易系统
     */
    async initializeTradingSystem(): Promise<ApiResponse> {
        try {
            const result = await this.electronAPI.tradingSystem.initialize();
            return result;
        } catch (error) {
            return {
                success: false,
                message: `初始化交易系统失败: ${error}`,
                data: null
            };
        }
    }

    /**
     * 关闭交易系统
     */
    async shutdownTradingSystem(): Promise<ApiResponse> {
        try {
            const result = await this.electronAPI.tradingSystem.shutdown();
            return result;
        } catch (error) {
            return {
                success: false,
                message: `关闭交易系统失败: ${error}`,
                data: null
            };
        }
    }

    /**
     * 获取行情连接状态
     */
    async getMarketStatus(): Promise<ApiResponse> {
        try {
            const result = await this.electronAPI.tradingSystem.getMarketStatus();
            return result;
        } catch (error) {
            return {
                success: false,
                message: `获取行情状态失败: ${error}`,
                data: null
            };
        }
    }

    /**
     * 获取交易连接状态
     */
    async getTradingStatus(): Promise<ApiResponse> {
        try {
            const result = await this.electronAPI.tradingSystem.getTradingStatus();
            return result;
        } catch (error) {
            return {
                success: false,
                message: `获取交易状态失败: ${error}`,
                data: null
            };
        }
    }

    // ===== 任务管理 =====

    /**
     * 创建交易任务
     */
    async createTask(taskConfig: TaskConfig): Promise<ApiResponse> {
        try {
            const result = await this.electronAPI.task.create(taskConfig);
            return result;
        } catch (error) {
            return {
                success: false,
                message: `创建任务失败: ${error}`,
                data: null
            };
        }
    }

    /**
     * 启动任务
     */
    async startTask(taskId: string): Promise<ApiResponse> {
        try {
            const result = await this.electronAPI.task.start({ taskId });
            return result;
        } catch (error) {
            return {
                success: false,
                message: `启动任务失败: ${error}`,
                data: null
            };
        }
    }

    /**
     * 停止任务
     */
    async stopTask(taskId: string): Promise<ApiResponse> {
        try {
            const result = await this.electronAPI.task.stop({ taskId });
            return result;
        } catch (error) {
            return {
                success: false,
                message: `停止任务失败: ${error}`,
                data: null
            };
        }
    }

    /**
     * 删除任务
     */
    async deleteTask(taskId: string): Promise<ApiResponse> {
        try {
            const result = await this.electronAPI.task.delete({ taskId });
            return result;
        } catch (error) {
            return {
                success: false,
                message: `删除任务失败: ${error}`,
                data: null
            };
        }
    }

    /**
     * 获取任务列表
     */
    async getTaskList(): Promise<ApiResponse> {
        try {
            const result = await this.electronAPI.task.getList();
            return result;
        } catch (error) {
            return {
                success: false,
                message: `获取任务列表失败: ${error}`,
                data: null
            };
        }
    }

    /**
     * 获取任务详情
     */
    async getTaskDetail(taskId: string): Promise<ApiResponse> {
        try {
            const result = await this.electronAPI.task.getStatus({ taskId });
            return result;
        } catch (error) {
            return {
                success: false,
                message: `获取任务详情失败: ${error}`,
                data: null
            };
        }
    }

    // ===== 事件监听 =====

    /**
     * 监听实时数据
     */
    onRealtimeData(callback: (data: RealtimeDataEvent) => void): void {
        this.eventListeners.set("realtimeData", (event, data) => callback(data));
    }

    /**
     * 监听连接状态变化
     */
    onConnectionStatus(callback: (data: ConnectionStatusEvent) => void): void {
        this.eventListeners.set("connectionStatus", (event, data) => callback(data));
    }

    /**
     * 监听错误事件
     */
    onError(callback: (data: ErrorEvent) => void): void {
        this.eventListeners.set("error", (event, data) => callback(data));
    }

    /**
     * 监听系统状态变化
     */
    onSystemStatus(callback: (data: any) => void): void {
        this.eventListeners.set("systemStatus", (event, data) => callback(data));
    }

    /**
     * 监听系统初始化完成
     */
    onSystemInitialized(callback: (data: any) => void): void {
        this.eventListeners.set("systemInitialized", (event, data) => callback(data));
    }

    /**
     * 监听系统心跳
     */
    onSystemHeartbeat(callback: (data: any) => void): void {
        this.eventListeners.set("systemHeartbeat", (event, data) => callback(data));
    }

    /**
     * 移除事件监听器
     */
    removeListener(eventType: string): void {
        this.eventListeners.delete(eventType);
    }

    /**
     * 清理所有事件监听器
     */
    destroy(): void {
        this.eventListeners.clear();
    }

    // ===== 窗口控制 =====

    /**
     * 最小化窗口
     */
    minimizeWindow(): void {
        this.electronAPI.window.minimize();
    }

    /**
     * 最大化窗口
     */
    maximizeWindow(): void {
        this.electronAPI.window.maximize();
    }

    /**
     * 关闭窗口
     */
    closeWindow(): void {
        this.electronAPI.window.close();
    }

    /**
     * 切换开发者工具
     */
    toggleDevTools(): void {
        this.electronAPI.window.toggleDevTools();
    }

    /**
     * 重新加载窗口
     */
    reloadWindow(): void {
        this.electronAPI.window.reload();
    }
}

// 懒加载单例
let _electronTradingClient: ElectronTradingClient | null = null;

export const electronTradingClient = {
    getInstance(): ElectronTradingClient {
        if (!_electronTradingClient) {
            if (typeof window === "undefined" || !window.electronAPI) {
                throw new Error("Electron API 不可用。当前不在 Electron 环境中。");
            }
            _electronTradingClient = new ElectronTradingClient();
        }
        return _electronTradingClient;
    },

    // 代理所有方法到实例
    async getTradingSystemStatus() {
        return this.getInstance().getTradingSystemStatus();
    },

    async initializeTradingSystem() {
        return this.getInstance().initializeTradingSystem();
    },

    async shutdownTradingSystem() {
        return this.getInstance().shutdownTradingSystem();
    },

    async getTaskList() {
        return this.getInstance().getTaskList();
    },

    async createTask(taskConfig: any) {
        return this.getInstance().createTask(taskConfig);
    },

    async startTask(taskId: string) {
        return this.getInstance().startTask(taskId);
    },

    async stopTask(taskId: string) {
        return this.getInstance().stopTask(taskId);
    },

    async deleteTask(taskId: string) {
        return this.getInstance().deleteTask(taskId);
    },

    onRealtimeData(callback: (data: RealtimeDataEvent) => void) {
        return this.getInstance().onRealtimeData(callback);
    },

    onConnectionStatus(callback: (data: ConnectionStatusEvent) => void) {
        return this.getInstance().onConnectionStatus(callback);
    },

    onSystemHeartbeat(callback: (data: any) => void) {
        return this.getInstance().onSystemHeartbeat(callback);
    },

    onError(callback: (data: ErrorEvent) => void) {
        return this.getInstance().onError(callback);
    },

    onSystemStatus(callback: (data: any) => void) {
        return this.getInstance().onSystemStatus(callback);
    },

    destroy() {
        if (_electronTradingClient) {
            _electronTradingClient.destroy();
            _electronTradingClient = null;
        }
    }
};
